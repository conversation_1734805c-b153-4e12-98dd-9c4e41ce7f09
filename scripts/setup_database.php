<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

try {
    // Create PDO connection
    $dsn = "mysql:host={$_ENV['DB_HOST']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $dbName = $_ENV['DB_NAME'];
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$dbName`");
    
    echo "✅ Database '$dbName' created/verified successfully\n";
    
    // Create calls table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS calls (
            id INT AUTO_INCREMENT PRIMARY KEY,
            call_id VARCHAR(255) NOT NULL UNIQUE,
            recording_url TEXT,
            start_date DATETIME,
            call_direction VARCHAR(50),
            duration INT,
            agent_name VARCHAR(255),
            customer_phone VARCHAR(50),
            transcription_text TEXT,
            transcription_confidence DECIMAL(3,2),
            ai_summary TEXT,
            ai_sentiment VARCHAR(50),
            ai_topics JSON,
            ai_action_items JSON,
            ai_satisfaction_score INT,
            processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_call_id (call_id),
            INDEX idx_customer_phone (customer_phone),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Calls table created successfully\n";
    
    // Create surveys table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS surveys (
            id INT AUTO_INCREMENT PRIMARY KEY,
            survey_id VARCHAR(255) NOT NULL UNIQUE,
            call_id VARCHAR(255) NOT NULL,
            customer_phone VARCHAR(50) NOT NULL,
            survey_url VARCHAR(500) NOT NULL,
            whatsapp_message_id VARCHAR(255),
            whatsapp_status ENUM('pending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'pending',
            survey_status ENUM('pending', 'completed', 'expired') DEFAULT 'pending',
            satisfaction_score INT,
            survey_responses JSON,
            submitted_at TIMESTAMP NULL,
            expires_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (call_id) REFERENCES calls(call_id) ON DELETE CASCADE,
            INDEX idx_survey_id (survey_id),
            INDEX idx_call_id (call_id),
            INDEX idx_customer_phone (customer_phone),
            INDEX idx_survey_status (survey_status),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Surveys table created successfully\n";
    
    // Create analytics table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS analytics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            call_id VARCHAR(255) NOT NULL,
            survey_id VARCHAR(255),
            customer_phone VARCHAR(50) NOT NULL,
            call_satisfaction_score INT,
            survey_satisfaction_score INT,
            ai_sentiment VARCHAR(50),
            ai_topics JSON,
            ai_action_items JSON,
            survey_responses JSON,
            combined_score DECIMAL(3,2),
            insights TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (call_id) REFERENCES calls(call_id) ON DELETE CASCADE,
            FOREIGN KEY (survey_id) REFERENCES surveys(survey_id) ON DELETE SET NULL,
            INDEX idx_call_id (call_id),
            INDEX idx_survey_id (survey_id),
            INDEX idx_customer_phone (customer_phone),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Analytics table created successfully\n";
    
    // Create WhatsApp logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS whatsapp_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            survey_id VARCHAR(255) NOT NULL,
            message_id VARCHAR(255),
            phone_number VARCHAR(50) NOT NULL,
            message_text TEXT,
            status VARCHAR(50),
            error_message TEXT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (survey_id) REFERENCES surveys(survey_id) ON DELETE CASCADE,
            INDEX idx_survey_id (survey_id),
            INDEX idx_phone_number (phone_number),
            INDEX idx_sent_at (sent_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ WhatsApp logs table created successfully\n";
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "📊 Tables created:\n";
    echo "   - calls (call data and AI analysis)\n";
    echo "   - surveys (survey links and responses)\n";
    echo "   - analytics (combined insights)\n";
    echo "   - whatsapp_logs (message delivery tracking)\n";
    
} catch (PDOException $e) {
    echo "❌ Database setup failed: " . $e->getMessage() . "\n";
    exit(1);
} 