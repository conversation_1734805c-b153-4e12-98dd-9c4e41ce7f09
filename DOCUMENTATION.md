# AI Call Summarizer - Complete System Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture & Data Flow](#architecture--data-flow)
3. [How Call Recordings Work](#how-call-recordings-work)
4. [Integration Methods](#integration-methods)
5. [API Reference](#api-reference)
6. [File Structure](#file-structure)
7. [Configuration](#configuration)
8. [Deployment Guide](#deployment-guide)
9. [Troubleshooting](#troubleshooting)
10. [Recent Updates & Features](#recent-updates--features)

---

## System Overview

The AI Call Summarizer is a complete PHP-based system that automatically:
1. **Receives** call metadata from your CRM system
2. **Downloads** audio recordings from URLs (including private/internal URLs)
3. **Transcribes** Hebrew audio to text using AssemblyAI
4. **Analyzes** transcripts using OpenAI GPT-4o-mini
5. **Returns** structured insights and summaries

### What You Get
- **Call Summary**: Concise description of the call purpose and outcome
- **Sentiment Analysis**: Customer mood (Positive/Neutral/Negative)
- **Key Topics**: Main subjects discussed
- **Action Items**: Specific tasks that need follow-up
- **Satisfaction Score**: 1-10 rating of customer satisfaction

### Key Features
- **Private URL Support**: Automatically handles internal/private audio URLs
- **Long Call Support**: Optimized for calls up to 30 minutes
- **Enhanced Error Logging**: Detailed error tracking and debugging
- **Multiple Integration Methods**: Web API, CLI, and direct PHP library
- **Robust Audio Processing**: Handles various audio formats and access methods

---

## Architecture & Data Flow

```
CRM System → Your Integration → AI Call Summarizer → Enriched Data
     ↓              ↓                    ↓              ↓
Call Metadata → JSON Payload → Transcription + Analysis → Structured Results
```

### Step-by-Step Process

1. **Trigger**: Your CRM system detects a completed call
2. **Data Preparation**: CRM creates JSON with call metadata + audio URL
3. **API Call**: Your system sends data to our API
4. **Audio Processing**: System downloads and transcribes audio (with private URL support)
5. **AI Analysis**: GPT-4o-mini analyzes the transcript
6. **Response**: System returns enriched data with all insights

---

## How Call Recordings Work

### Audio Requirements
- **Format**: MP3, WAV, M4A, or any format supported by AssemblyAI
- **Access**: Can be public URLs or private/internal URLs (system handles both)
- **Language**: Optimized for Hebrew (but supports other languages)
- **Size**: Up to 1GB per file
- **Duration**: Up to 30 minutes (configurable timeout)

### Audio URL Examples
```json
{
  "recordingPath": "https://your-crm.com/recordings/call_12345.mp3"
}
```

### Private URL Support
The system automatically detects and handles private/internal URLs:
- **Detection**: URLs from internal domains (like `master.ippbx.co.il`)
- **Processing**: Downloads audio file and uploads directly to AssemblyAI
- **Fallback**: Multiple methods ensure successful processing

### What Happens to Your Audio
1. **Detection**: System identifies if URL is private or public
2. **Download**: System downloads audio file from your URL
3. **Upload**: Audio is sent to AssemblyAI for processing
4. **Transcription**: AssemblyAI converts Hebrew speech to text
5. **Analysis**: Text is analyzed by OpenAI GPT-4o-mini
6. **Cleanup**: Audio files are not stored permanently

---

## Integration Methods

### Method 1: Web API (Recommended)
**Best for**: Real-time processing, web applications, CRM integrations

```bash
# Single call processing
curl -X POST http://your-server.com/process-call \
  -H "Content-Type: application/json" \
  -d '{
    "callid": 12345,
    "recordingPath": "https://your-crm.com/recordings/call_12345.mp3",
    "start_date": "2025-01-01 12:00:00",
    "call_direction": "OUTBOUND"
  }'
```

### Method 2: CLI Script
**Best for**: Batch processing, scheduled tasks, testing

```bash
php cli.php --process '{"callid": 123, "recordingPath": "https://..."}'
```

### Method 3: Direct PHP Library
**Best for**: Custom applications, advanced integrations

```php
<?php
require_once 'vendor/autoload.php';
use App\CallSummarizer;

$summarizer = new CallSummarizer();
$result = $summarizer->processCall($crmData);
```

---

## API Reference

### Base URL
```
http://your-server.com/
```

### Endpoints

#### 1. Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "healthy",
  "service": "AI Call Summarizer",
  "version": "1.0.0",
  "timestamp": "2025-01-01 12:00:00"
}
```

#### 2. Process Single Call
```http
POST /process-call
Content-Type: application/json
```
**Request Body:**
```json
{
  "callid": "39128557",
  "call_direction": "OUTBOUND",
  "caller": "300",
  "caller_name": "esim **********",
  "callee": "113690515029966",
  "callee_name": "",
  "start_date": "2025-06-26 17:33:54",
  "call_sec": 31,
  "recordingPath": "https://master.ippbx.co.il//fs_sounds/recordings/0/3966/auto_rec/2025-06-26/22ec6cf1-aee9-4241-b7d5-bf139ccca0a7.wav"
}
```

**Note:** The system automatically determines the customer phone number based on call direction:
- **OUTBOUND calls**: Customer = `callee`, Agent = `caller`
- **INBOUND calls**: Customer = `caller`, Agent = `callee`
- Phone numbers are automatically formatted for WhatsApp (e.g., `+972526235589`)
- If phone numbers are embedded in name fields, they are automatically extracted
```

**Response:**
```json
{
  "call_details": {
    "callid": 12345,
    "recordingPath": "https://your-crm.com/recordings/call_12345.mp3",
    "start_date": "2025-01-01 12:00:00",
    "call_direction": "OUTBOUND"
  },
  "transcription_result": {
    "status": "completed",
    "transcript_id": "abc123-def456",
    "text": "שלום, אני מתעניין במוצר שלכם...",
    "language_code": "he",
    "confidence": 0.96,
    "error_message": null
  },
  "ai_analysis": {
    "status": "completed",
    "summary": "Customer inquired about product features and pricing",
    "call_sentiment": "Positive",
    "main_topics": ["Product inquiry", "Pricing", "Features"],
    "action_items": [
      {
        "task": "Send product catalog",
        "responsible": "Agent"
      },
      {
        "task": "Follow up on pricing quote",
        "responsible": "Agent"
      }
    ],
    "customer_satisfaction_score": 8,
    "error_message": null
  }
}
```

#### 3. Process Multiple Calls
```http
POST /process-calls
Content-Type: application/json
```
**Request Body:**
```json
{
  "calls": [
    {
      "callid": 12345,
      "recordingPath": "https://your-crm.com/recordings/call_12345.mp3"
    },
    {
      "callid": 12346,
      "recordingPath": "https://your-crm.com/recordings/call_12346.mp3"
    }
  ]
}
```

**Response:**
```json
{
  "total_calls": 2,
  "processed_calls": 2,
  "failed_calls": 0,
  "results": [
    {
      "index": 0,
      "result": { /* call result */ }
    },
    {
      "index": 1,
      "result": { /* call result */ }
    }
  ]
}
```

### Error Responses
```json
{
  "error": "Error type",
  "message": "Detailed error message"
}
```

---

## File Structure

```
ben-levy-ai-call-summarizer/
├── src/
│   ├── CallSummarizer.php          # Main processing class
│   └── AudioProxy.php              # Audio file proxy service
├── public/
│   ├── index.php                   # Web API entry point
│   └── audio.php                   # Audio file server (for private URLs)
├── vendor/                         # Composer dependencies
├── composer.json                   # PHP dependencies
├── cli.php                         # Command-line interface
├── .env                            # Environment variables
├── DOCUMENTATION.md                # This file
└── .gitignore                      # Git ignore rules
```

---

## Configuration

### Environment Variables (.env file)
```env
# Required: AssemblyAI API Key
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# Required: OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Optional: OpenAI Model (default: gpt-4o-mini)
OPENAI_MODEL=gpt-4o-mini

# Optional: AI System Prompt (customizable for different analysis needs)
AI_SYSTEM_PROMPT="You are an expert call analyst for a business. Your task is to analyze the following call transcript and provide a structured summary in a strict JSON format. The call is in Hebrew.

Based on the transcript, you must extract the following information:
1. **summary**: A concise, neutral summary of the call's purpose and outcome.
2. **call_sentiment**: The overall sentiment of the customer. Options are: \"Positive\", \"Neutral\", \"Negative\".
3. **main_topics**: A list of the key topics discussed during the call.
4. **action_items**: A list of specific, actionable tasks for the business or agent, including who is responsible if mentioned. If none, provide an empty list.
5. **customer_satisfaction_score**: An estimated customer satisfaction score on a scale of 1 to 10, where 1 is \"Very Dissatisfied\" and 10 is \"Very Satisfied\".

Provide your response as a single JSON object ONLY. Do not include any introductory text, explanations, or markdown formatting like ```json."
```

### System Constants
```php
// Hebrew language code for transcription
HEBREW_LANGUAGE_CODE = "he"

// Maximum wait time for transcription (30 minutes for long calls)
TRANSCRIPTION_TIMEOUT_SECONDS = 1800

// Polling interval for transcription status (5 seconds)
POLLING_INTERVAL_SECONDS = 5

// Default OpenAI model
DEFAULT_OPENAI_MODEL = "gpt-4o-mini"

// HTTP client timeout (increased for longer operations)
HTTP_TIMEOUT = 60
```

---

## Deployment Guide

### Prerequisites
- PHP 8.0 or higher
- Composer
- Required PHP extensions: json, curl, mbstring, openssl

### Quick Setup
```bash
# 1. Clone repository
git clone <your-repo-url>
cd ben-levy-ai-call-summarizer

# 2. Install dependencies
composer install

# 3. Configure API keys
cp .env.example .env
# Edit .env with your API keys

# 4. Test the system
php cli.php --test
```

### Production Deployment
```bash
# 1. Install dependencies
composer install --no-dev --optimize-autoloader

# 2. Set up web server (Apache/Nginx)
# Point document root to public/ directory

# 3. Configure environment
cp .env.example .env
# Add your API keys

# 4. Set permissions
chmod 755 public/
chmod 644 .env

# 5. Test endpoints
curl http://your-server.com/health
```

### Web Server Configuration

#### Apache (.htaccess in public/)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^ index.php [QSA,L]
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

---

## Recent Updates & Features

### Enhanced Audio Processing
- **Private URL Support**: Automatically detects and handles internal URLs
- **Direct Upload**: Downloads private files and uploads to AssemblyAI
- **Fallback Methods**: Multiple approaches ensure successful processing
- **Audio Proxy**: Optional proxy service for complex URL scenarios

### Long Call Support
- **Extended Timeout**: Increased to 30 minutes for long calls
- **Optimized Polling**: Efficient status checking during transcription
- **Memory Management**: Better handling of large audio files

### Enhanced Error Logging
- **Detailed AssemblyAI Errors**: Full error context and debugging info
- **Timeout Tracking**: Logs when transcription times out with elapsed time
- **Upload Method Tracking**: Shows whether files were uploaded directly or referenced
- **Structured Logging**: JSON-formatted logs for better analysis

### Performance Improvements
- **HTTP Timeout Optimization**: Increased timeouts for longer operations
- **Model Optimization**: Switched to gpt-4o-mini for better performance/cost ratio
- **Error Recovery**: Better handling of network issues and API failures

### AI System Prompt Customization
- **Environment Variable**: System prompt is now configurable via `AI_SYSTEM_PROMPT`
- **Easy Swapping**: Change analysis behavior without code modifications
- **Fallback Support**: Default prompt used if environment variable is not set
- **Multi-language Support**: Can be customized for different languages or analysis needs

---

## AI System Prompt Customization

The AI system prompt is now fully configurable through the `AI_SYSTEM_PROMPT` environment variable. This allows you to easily customize the analysis behavior without modifying code.

### Default Prompt
The system comes with a default prompt optimized for Hebrew call analysis that extracts:
- Call summary
- Customer sentiment
- Main topics discussed
- Action items
- Customer satisfaction score

### Customizing the Prompt

#### Example 1: English Call Analysis
```env
AI_SYSTEM_PROMPT="You are an expert call analyst. Analyze this English call transcript and provide a structured JSON summary with: summary, sentiment (Positive/Neutral/Negative), topics, action_items, and satisfaction_score (1-10)."
```

#### Example 2: Sales Call Focus
```env
AI_SYSTEM_PROMPT="You are a sales call analyst. Analyze this Hebrew sales call and extract: call_summary, lead_quality (Hot/Warm/Cold), objections_raised, next_steps, and conversion_probability (1-100%)."
```

#### Example 3: Customer Service Focus
```env
AI_SYSTEM_PROMPT="You are a customer service analyst. Analyze this Hebrew support call and identify: issue_type, resolution_status, escalation_needed, customer_effort_score (1-5), and follow_up_required."
```

#### Example 4: Multi-language Support
```env
AI_SYSTEM_PROMPT="You are a multilingual call analyst. Analyze this call transcript (which may be in Hebrew, English, or Arabic) and provide: language_detected, call_summary, sentiment, key_points, and priority_level (High/Medium/Low)."
```

### Prompt Best Practices

1. **Clear Instructions**: Be specific about what you want extracted
2. **JSON Format**: Always specify the exact JSON structure expected
3. **Language Specification**: Mention the expected language(s)
4. **Field Definitions**: Define what each field should contain
5. **Response Format**: Specify that only JSON should be returned

### Testing Custom Prompts

```bash
# Test with a custom prompt
export AI_SYSTEM_PROMPT="Your custom prompt here"
php cli.php --process '{"callid": 123, "recordingPath": "https://..."}'

# Or update .env file and restart the service
echo 'AI_SYSTEM_PROMPT="Your custom prompt"' >> .env
```

### Prompt Templates

#### Basic Template
```env
AI_SYSTEM_PROMPT="You are a [ROLE]. Analyze this [LANGUAGE] call transcript and provide a JSON response with: [FIELD1], [FIELD2], [FIELD3]..."
```

#### Advanced Template
```env
AI_SYSTEM_PROMPT="You are a [ROLE] specializing in [DOMAIN]. Analyze this [LANGUAGE] call transcript and extract the following information in JSON format:

1. **summary**: [DESCRIPTION]
2. **sentiment**: [OPTIONS]
3. **topics**: [DESCRIPTION]
4. **action_items**: [DESCRIPTION]
5. **score**: [SCALE]

Provide your response as a single JSON object ONLY."
```

---

## Integration Examples

### Example 1: CRM Webhook Integration
```php
<?php
// Your CRM system calls this when a call is completed
function processCompletedCall($callData) {
    $apiUrl = 'http://your-ai-summarizer.com/process-call';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($callData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    $result = json_decode($response, true);
    
    // Store results in your database
    saveCallAnalysis($callData['callid'], $result);
}
```

### Example 2: Batch Processing Script
```php
<?php
// Process multiple calls from your database
function processBatchCalls() {
    $pendingCalls = getPendingCallsFromDatabase();
    
    $batchData = ['calls' => []];
    foreach ($pendingCalls as $call) {
        $batchData['calls'][] = [
            'callid' => $call['id'],
            'recordingPath' => $call['recording_url'],
            'start_date' => $call['start_time'],
            'call_direction' => $call['direction']
        ];
    }
    
    $apiUrl = 'http://your-ai-summarizer.com/process-calls';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($batchData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    $results = json_decode($response, true);
    
    // Update database with results
    foreach ($results['results'] as $result) {
        if (isset($result['result'])) {
            updateCallAnalysis($result['result']);
        }
    }
}
```

### Example 3: Scheduled Task
```bash
#!/bin/bash
# cron job to process calls every hour
cd /path/to/ai-call-summarizer
php cli.php --process "$(getNextCallFromDatabase)"
```

---

## Troubleshooting

### Common Issues

#### 1. "Missing API Keys" Error
**Problem**: System can't find AssemblyAI or OpenAI API keys
**Solution**: 
```bash
# Check if .env file exists and has keys
cat .env

# Make sure keys are not empty
ASSEMBLYAI_API_KEY=your_actual_key_here
OPENAI_API_KEY=your_actual_key_here
```

#### 2. "Transcription Failed" Error
**Problem**: Audio file can't be transcribed
**Solutions**:
- Check if audio URL is accessible
- Verify audio format is supported
- Check AssemblyAI API quota
- Ensure audio file is not corrupted
- Check logs for detailed error messages

#### 3. "AI Analysis Failed" Error
**Problem**: GPT-4o-mini can't analyze the transcript
**Solutions**:
- Check OpenAI API key and quota
- Verify network connectivity
- Check if transcript is not empty
- Review logs for specific error messages

#### 4. "Timeout" Error
**Problem**: Transcription takes too long
**Solutions**:
- Check audio file size (should be under 1GB)
- Verify audio quality
- Check AssemblyAI service status
- System now supports up to 30 minutes for long calls

#### 5. "Private URL Access" Error
**Problem**: Can't access internal/private audio URLs
**Solutions**:
- System automatically handles private URLs
- Check if the URL is accessible from your server
- Verify authentication if required
- Check logs for specific access errors

### Log Analysis
```bash
# View real-time logs
tail -f logs/call_summarizer-$(date +%Y-%m-%d).log

# Search for errors
grep "ERROR" logs/call_summarizer-*.log

# Search for specific call ID
grep "callid: 12345" logs/call_summarizer-*.log

# Search for upload method
grep "upload_method" logs/call_summarizer-*.log
```

### Performance Monitoring
```bash
# Check API response times
curl -w "@curl-format.txt" -o /dev/null -s "http://your-server.com/health"

# Monitor log file size
ls -lh logs/call_summarizer-*.log

# Check PHP memory usage
php -i | grep memory_limit
```

---

## Security Considerations

### API Key Security
- Store API keys in environment variables only
- Never commit .env file to version control
- Use different keys for development and production
- Rotate keys regularly

### Input Validation
- All inputs are validated before processing
- Audio URLs are checked for accessibility
- JSON payloads are validated for required fields
- Error messages don't expose sensitive information

### Network Security
- Use HTTPS for all API communications
- Validate audio URLs are from trusted domains
- Implement rate limiting if needed
- Monitor for unusual API usage patterns

### Audio File Security
- Audio files are not stored permanently
- Temporary files are cleaned up after processing
- Private URLs are handled securely
- No audio content is logged

---

## Support & Maintenance

### Regular Maintenance
1. **Monitor logs** for errors and performance issues
2. **Check API quotas** for AssemblyAI and OpenAI
3. **Update dependencies** regularly with `composer update`
4. **Backup configuration** and log files
5. **Test system** periodically with sample data

### Getting Help
1. Check this documentation first
2. Review log files for error details
3. Test with sample data to isolate issues
4. Verify API keys and quotas
5. Check system requirements and dependencies

### Performance Optimization
- Use batch processing for multiple calls
- Implement caching for repeated requests
- Monitor and optimize database queries
- Consider using PHP-FPM for better performance
- Use CDN for static assets if applicable

---

## 📋 **Step-by-Step Process Breakdown**

### **Step 1: Receive & Validate Payload**
```php
// Your payload comes in as JSON
$crmData = [
    'callid' => 39079944,
    'call_direction' => 'OUTBOUND',
    'caller' => '300',
    'caller_name' => '300',
    // ... all your other fields ...
    'recordingPath' => 'https://master.ippbx.co.il//fs_sounds/recordings/0/3903/auto_rec/2025-06-25/40bd8ffb-a55b-4fbf-95a9-6c16167087b2.wav'
];

// Our code validates:
// 1. recordingPath exists and is not empty
// 2. URL is accessible
```

### **Step 2: Extract Audio URL & Detect Type**
```php
// Our code extracts the recordingPath
$audioUrl = 'https://master.ippbx.co.il//fs_sounds/recordings/0/3903/auto_rec/2025-06-25/40bd8ffb-a55b-4fbf-95a9-6c16167087b2.wav';

// System detects this is a private URL (internal domain)
// Automatically switches to direct upload method
```

### **Step 3: Download & Upload to AssemblyAI**
```php
// For private URLs, our code:
// 1. Downloads the audio file
// 2. Uploads it directly to AssemblyAI's CDN
// 3. Gets back a public AssemblyAI URL

$assemblyaiUrl = 'https://cdn.assemblyai.com/upload/e2f3fbcc-0198-41ce-a4d7-c37d26188d37';
```

### **Step 4: Submit to AssemblyAI for Transcription**
```php
// Our code sends this to AssemblyAI API:
$assemblyaiPayload = [
    'audio_url' => 'https://cdn.assemblyai.com/upload/e2f3fbcc-0198-41ce-a4d7-c37d26188d37',
    'language_code' => 'he'  // Hebrew language
];

// POST to: https://api.assemblyai.com/v2/transcript
// Headers: Authorization: YOUR_ASSEMBLYAI_API_KEY
```

### **Step 5: Poll AssemblyAI for Completion**
```php
// AssemblyAI returns a transcript ID like: "ee73bbb0-1a8c-4db6-9bcd-53e9439b622f"
$transcriptId = "ee73bbb0-1a8c-4db6-9bcd-53e9439b622f";

// Our code polls every 5 seconds (up to 30 minutes for long calls):
while (true) {
    // GET https://api.assemblyai.com/v2/transcript/{transcriptId}
    
    if ($status === 'completed') {
        // Get the transcript text
        $transcriptText = "רועה נכון? נכון. מה נשמע? בסדר, בואי תעשי לי רגע סדר...";
        break;
    }
    
    if ($status === 'error') {
        // Handle error with detailed logging
        break;
    }
    
    sleep(5); // Wait 5 seconds before next poll
}
```

### **Step 6: Send Transcript to OpenAI GPT-4o-mini**
```php
// Our code sends this to OpenAI API:
$openaiPayload = [
    'model' => 'gpt-4o-mini',  // Optimized model for performance/cost
    'messages' => [
        [
            'role' => 'system',
            'content' => 'You are an expert call analyst... Analyze this Hebrew transcript...'
        ],
        [
            'role' => 'user', 
            'content' => 'רועה נכון? נכון. מה נשמע? בסדר, בואי תעשי לי רגע סדר...'  // The Hebrew transcript
        ]
    ],
    'temperature' => 0.3,
    'max_tokens' => 1000
];

// POST to: https://api.openai.com/v1/chat/completions
// Headers: Authorization: Bearer YOUR_OPENAI_API_KEY
```

### **Step 7: Parse AI Response**
```php
// OpenAI returns JSON like:
$aiResponse = '{
    "summary": "The call involved a discussion between a ceramic artist and a representative about the CRM system and its potential benefits for managing customer leads and communications. The artist expressed concerns about the suitability of the system for her small business and preferred to focus on WhatsApp marketing instead.",
    "call_sentiment": "Neutral", 
    "main_topics": ["CRM system discussion", "Customer lead management", "Marketing strategies", "Concerns about system suitability", "WhatsApp as a primary communication tool"],
    "action_items": [
        {"task": "Send a link to the CRM tutorial", "responsible": "Agent"},
        {"task": "Follow the customer on Instagram", "responsible": "Agent"}
    ],
    "customer_satisfaction_score": 6
}';

// Our code parses this and returns final result
```

### **Step 8: Return Complete Result**
```php
// Final response to you:
$finalResult = [
    'call_details' => [
        'callid' => 39079944,
        'call_direction' => 'OUTBOUND',
        'caller' => '300',
        'caller_name' => '300',
        // ... all your original data ...
        'recordingPath' => 'https://master.ippbx.co.il//fs_sounds/recordings/0/3903/auto_rec/2025-06-25/40bd8ffb-a55b-4fbf-95a9-6c16167087b2.wav'
    ],
    'transcription_result' => [
        'status' => 'completed',
        'transcript_id' => 'ee73bbb0-1a8c-4db6-9bcd-53e9439b622f',
        'text' => 'רועה נכון? נכון. מה נשמע? בסדר, בואי תעשי לי רגע סדר...',
        'language_code' => 'he',
        'confidence' => 0.81243134,
        'error_message' => null
    ],
    'ai_analysis' => [
        'status' => 'completed',
        'summary' => 'The call involved a discussion between a ceramic artist and a representative about the CRM system and its potential benefits for managing customer leads and communications. The artist expressed concerns about the suitability of the system for her small business and preferred to focus on WhatsApp marketing instead.',
        'call_sentiment' => 'Neutral',
        'main_topics' => ['CRM system discussion', 'Customer lead management', 'Marketing strategies', 'Concerns about system suitability', 'WhatsApp as a primary communication tool'],
        'action_items' => [
            {'task': 'Send a link to the CRM tutorial', 'responsible': 'Agent'},
            {'task': 'Follow the customer on Instagram', 'responsible': 'Agent'}
        ],
        'customer_satisfaction_score' => 6,
        'error_message' => null
    ]
];
```

## ⚙️ **Key Points About Your Payload:**

1. **Audio File**: Your WAV file at `recordingPath` will be downloaded and sent to AssemblyAI
2. **Call Duration**: Supports calls up to 30 minutes (756 seconds in your example)
3. **Call Status**: Works with both answered and unanswered calls
4. **Language**: Optimized for Hebrew (`language_code: 'he'`)
5. **Private URLs**: Automatically handles internal URLs like `master.ippbx.co.il`

## ⚙️ **Configuration Notes:**

- **AssemblyAI API Key**: From your `.env` file
- **OpenAI API Key**: From your `.env` file  
- **Model**: Set to `gpt-4o-mini` for optimal performance/cost ratio
- **Timeout**: 30 minutes maximum for transcription (configurable)
- **Polling**: Every 5 seconds to check transcription status
- **Private URL Handling**: Automatic detection and processing

## 🚀 **What Happens Next:**

1. **Your CRM** sends this payload to our API
2. **Our system** processes it automatically (handles private URLs)
3. **You get back** the enriched data with transcription and AI analysis
4. **Total time**: Usually 30-60 seconds for short calls, 2-5 minutes for long calls

The system is now production-ready with enhanced features for handling private URLs, long calls, and detailed error logging. 