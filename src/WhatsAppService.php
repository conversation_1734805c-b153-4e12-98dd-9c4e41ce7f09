<?php

namespace App;

use GuzzleHttp\Client;
use Monolog\Logger;
use Ramsey\Uuid\Uuid;

class WhatsAppService
{
    private Client $httpClient;
    private Logger $logger;
    private string $apiId;
    private string $apiToken;
    private string $instanceId;
    private string $baseUrl;
    
    public function __construct()
    {
        $this->validateEnvironment();
        $this->setupLogger();
        $this->setupHttpClient();
        $this->loadApiKeys();
    }
    
    private function validateEnvironment(): void
    {
        $requiredVars = [
            'GREENAPI_ID',
            'GREENAPI_TOKEN', 
            'GREENAPI_INSTANCE_ID'
        ];
        
        foreach ($requiredVars as $var) {
            if (empty($_ENV[$var])) {
                throw new \Exception("Missing required environment variable: $var");
            }
        }
    }
    
    private function setupLogger(): void
    {
        $this->logger = new Logger('whatsapp_service');
        $this->logger->pushHandler(new \Monolog\Handler\StreamHandler(
            'logs/whatsapp_service.log',
            Logger::INFO
        ));
    }
    
    private function setupHttpClient(): void
    {
        $this->httpClient = new Client([
            'timeout' => 30,
            'connect_timeout' => 10,
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent' => 'AI-Call-Summarizer/1.0'
            ]
        ]);
    }
    
    private function loadApiKeys(): void
    {
        $this->apiId = $_ENV['GREENAPI_ID'];
        $this->apiToken = $_ENV['GREENAPI_TOKEN'];
        $this->instanceId = $_ENV['GREENAPI_INSTANCE_ID'];
        $this->baseUrl = "https://api.green-api.com";
    }
    
    /**
     * Send WhatsApp message with survey link
     * 
     * @param string $phoneNumber Phone number in international format (e.g., 972501234567)
     * @param string $surveyUrl Survey URL to send
     * @param string $callId Associated call ID for tracking
     * @return array
     */
    public function sendSurveyMessage(string $phoneNumber, string $surveyUrl, string $callId): array
    {
        try {
            $this->logger->info("Sending WhatsApp survey message", [
                'phone_number' => $phoneNumber,
                'call_id' => $callId,
                'survey_url' => $surveyUrl
            ]);
            
            // Format phone number (remove + if present)
            $formattedPhone = ltrim($phoneNumber, '+');
            
            // Create message text
            $messageText = $this->createSurveyMessage($surveyUrl);
            
            // Send message via GreenAPI
            $response = $this->sendMessage($formattedPhone, $messageText);
            
            if ($response['success']) {
                $this->logger->info("WhatsApp message sent successfully", [
                    'phone_number' => $phoneNumber,
                    'message_id' => $response['message_id'],
                    'call_id' => $callId
                ]);
                
                return [
                    'status' => 'sent',
                    'message_id' => $response['message_id'],
                    'phone_number' => $phoneNumber,
                    'error_message' => null
                ];
            } else {
                throw new \Exception($response['error'] ?? 'Unknown error');
            }
            
        } catch (\Exception $e) {
            $this->logger->error("WhatsApp message failed", [
                'phone_number' => $phoneNumber,
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'failed',
                'message_id' => null,
                'phone_number' => $phoneNumber,
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send message via GreenAPI
     * 
     * @param string $phoneNumber
     * @param string $message
     * @return array
     */
    private function sendMessage(string $phoneNumber, string $message): array
    {
        $url = "{$this->baseUrl}/waInstance{$this->instanceId}/SendMessage/{$this->apiToken}";
        
        $payload = [
            'chatId' => $phoneNumber . '@c.us',
            'message' => $message
        ];
        
        try {
            $response = $this->httpClient->post($url, [
                'json' => $payload
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);
            
            if (isset($data['idMessage'])) {
                return [
                    'success' => true,
                    'message_id' => $data['idMessage']
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'No message ID in response'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create survey message text
     * 
     * @param string $surveyUrl
     * @return string
     */
    private function createSurveyMessage(string $surveyUrl): string
    {
        return "שלום! תודה שהתקשרת אלינו. 

אנחנו רוצים לשמוע את דעתך על השיחה שלנו. זה ייקח רק דקה!

לחץ על הקישור הבא כדי למלא סקר קצר:
{$surveyUrl}

תודה על הזמן שלך! 🙏";
    }
    
    /**
     * Check message delivery status
     * 
     * @param string $messageId
     * @return array
     */
    public function checkDeliveryStatus(string $messageId): array
    {
        try {
            $url = "{$this->baseUrl}/waInstance{$this->instanceId}/GetNotification/{$this->apiToken}";
            
            $response = $this->httpClient->get($url);
            $data = json_decode($response->getBody()->getContents(), true);
            
            // Look for our message ID in notifications
            foreach ($data as $notification) {
                if (isset($notification['body']['idMessage']) && 
                    $notification['body']['idMessage'] === $messageId) {
                    
                    $status = $this->mapDeliveryStatus($notification['body']['status']);
                    
                    return [
                        'status' => 'found',
                        'delivery_status' => $status,
                        'timestamp' => $notification['timestamp'] ?? null
                    ];
                }
            }
            
            return [
                'status' => 'not_found',
                'delivery_status' => 'unknown'
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to check delivery status", [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'error',
                'delivery_status' => 'unknown',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Map GreenAPI status to our status enum
     * 
     * @param string $greenApiStatus
     * @return string
     */
    private function mapDeliveryStatus(string $greenApiStatus): string
    {
        $statusMap = [
            'sent' => 'sent',
            'delivered' => 'delivered',
            'read' => 'read',
            'failed' => 'failed'
        ];
        
        return $statusMap[$greenApiStatus] ?? 'unknown';
    }
    
    /**
     * Test WhatsApp connection
     * 
     * @return array
     */
    public function testConnection(): array
    {
        try {
            $url = "{$this->baseUrl}/waInstance{$this->instanceId}/GetStateInstance/{$this->apiToken}";
            
            $response = $this->httpClient->get($url);
            $data = json_decode($response->getBody()->getContents(), true);
            
            if (isset($data['stateInstance'])) {
                return [
                    'status' => 'connected',
                    'state' => $data['stateInstance'],
                    'error_message' => null
                ];
            } else {
                return [
                    'status' => 'error',
                    'state' => 'unknown',
                    'error_message' => 'Invalid response format'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'state' => 'unknown',
                'error_message' => $e->getMessage()
            ];
        }
    }
} 