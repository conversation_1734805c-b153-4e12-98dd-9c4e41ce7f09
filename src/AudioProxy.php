<?php

namespace App;

use GuzzleHttp\Client;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Formatter\LineFormatter;

/**
 * Audio Proxy Service
 * Downloads private audio files and serves them publicly for AssemblyAI
 */
class AudioProxy
{
    private Client $httpClient;
    private Logger $logger;
    private string $tempDir;
    
    public function __construct()
    {
        $this->setupLogger();
        $this->setupHttpClient();
        $this->tempDir = sys_get_temp_dir() . '/audio_proxy/';
        
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }
    
    /**
     * Setup logging
     */
    private function setupLogger(): void
    {
        $this->logger = new Logger('audio_proxy');
        
        // File handler
        $fileHandler = new StreamHandler(
            __DIR__ . '/../logs/audio_proxy.log',
            30, // Keep 30 days of logs
            Logger::INFO
        );
        $fileHandler->setFormatter(new LineFormatter(
            "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
            'Y-m-d H:i:s'
        ));
        
        // Console handler
        $consoleHandler = new StreamHandler('php://stdout', Logger::INFO);
        $consoleHandler->setFormatter(new LineFormatter(
            "[%datetime%] %level_name%: %message% %context%\n",
            'Y-m-d H:i:s'
        ));
        
        $this->logger->pushHandler($fileHandler);
        $this->logger->pushHandler($consoleHandler);
    }
    
    /**
     * Setup HTTP client
     */
    private function setupHttpClient(): void
    {
        $this->httpClient = new Client([
            'timeout' => 60,
            'connect_timeout' => 10,
            'headers' => [
                'User-Agent' => 'Audio-Proxy/1.0',
                'Accept' => 'audio/*',
            ]
        ]);
    }
    
    /**
     * Download audio file and return a public URL
     * 
     * @param string $privateUrl
     * @return string|null Public URL or null if failed
     */
    public function proxyAudioFile(string $privateUrl): ?string
    {
        try {
            $this->logger->info("Attempting to proxy audio file", ['url' => $privateUrl]);
            
            // Generate unique filename
            $filename = md5($privateUrl) . '_' . time() . '.wav';
            $filepath = $this->tempDir . $filename;
            
            // Download the file
            $response = $this->httpClient->get($privateUrl, [
                'stream' => true,
                'timeout' => 30
            ]);
            
            if ($response->getStatusCode() !== 200) {
                $this->logger->error("Failed to download audio file", [
                    'url' => $privateUrl,
                    'status_code' => $response->getStatusCode()
                ]);
                return null;
            }
            
            // Save to temp file
            $fileHandle = fopen($filepath, 'w');
            $stream = $response->getBody();
            
            while (!$stream->eof()) {
                fwrite($fileHandle, $stream->read(8192));
            }
            
            fclose($fileHandle);
            
            $this->logger->info("Successfully downloaded audio file", [
                'url' => $privateUrl,
                'filepath' => $filepath,
                'size' => filesize($filepath)
            ]);
            
            // Generate public URL for the file
            $publicUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/audio.php?file=' . $filename;
            
            $this->logger->info("Generated public URL for audio file", [
                'private_url' => $privateUrl,
                'public_url' => $publicUrl
            ]);
            
            return $publicUrl;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to proxy audio file", [
                'url' => $privateUrl,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Clean up old temporary files
     */
    public function cleanup(): void
    {
        $files = glob($this->tempDir . '*');
        $cutoff = time() - (24 * 60 * 60); // 24 hours
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
                $this->logger->info("Cleaned up old file", ['file' => $file]);
            }
        }
    }
} 