<?php

namespace App;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;

/**
 * AI Call Summarizer - End-to-End Pipeline
 * 
 * This class provides a complete workflow for:
 * 1. Transcribing audio calls using AssemblyAI
 * 2. Analyzing transcripts using OpenAI GPT-4
 * 3. Generating structured summaries and insights
 */
class CallSummarizer
{
    // Constants
    private const HEBREW_LANGUAGE_CODE = 'he';
    private const TRANSCRIPTION_TIMEOUT_SECONDS = 1800; // 30 minutes for long calls
    private const POLLING_INTERVAL_SECONDS = 5;
    private const DEFAULT_OPENAI_MODEL = 'gpt-4o-mini'; // Changed to gpt-4o-mini as requested
    
    // Properties
    private Client $httpClient;
    private Logger $logger;
    private string $assemblyaiApiKey;
    private string $openaiApiKey;
    private string $openaiModel;
    
    /**
     * Constructor - Initialize the CallSummarizer
     * 
     * @throws \InvalidArgumentException
     */
    public function __construct()
    {
        $this->validateEnvironment();
        $this->setupLogger();
        $this->setupHttpClient();
        $this->loadApiKeys();
    }
    
    /**
     * Validate that required environment variables are set
     * 
     * @throws \InvalidArgumentException
     */
    private function validateEnvironment(): void
    {
        $requiredVars = ['ASSEMBLYAI_API_KEY', 'OPENAI_API_KEY'];
        $missingVars = [];
        
        foreach ($requiredVars as $var) {
            if (empty($_ENV[$var])) {
                $missingVars[] = $var;
            }
        }
        
        if (!empty($missingVars)) {
            throw new \InvalidArgumentException(
                'Missing required environment variables: ' . implode(', ', $missingVars)
            );
        }
    }
    
    /**
     * Setup logging configuration
     */
    private function setupLogger(): void
    {
        $this->logger = new Logger('call_summarizer');
        
        // File handler with rotation
        $fileHandler = new RotatingFileHandler(
            __DIR__ . '/../logs/call_summarizer.log',
            30, // Keep 30 days of logs
            Logger::INFO
        );
        $fileHandler->setFormatter(new LineFormatter(
            "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
            'Y-m-d H:i:s'
        ));
        
        // Console handler
        $consoleHandler = new StreamHandler('php://stdout', Logger::INFO);
        $consoleHandler->setFormatter(new LineFormatter(
            "[%datetime%] %level_name%: %message% %context%\n",
            'Y-m-d H:i:s'
        ));
        
        $this->logger->pushHandler($fileHandler);
        $this->logger->pushHandler($consoleHandler);
    }
    
    /**
     * Setup HTTP client for API calls
     */
    private function setupHttpClient(): void
    {
        $this->httpClient = new Client([
            'timeout' => 60, // Increased for longer operations
            'connect_timeout' => 10,
            'headers' => [
                'User-Agent' => 'AI-Call-Summarizer/1.0',
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ]
        ]);
    }
    
    /**
     * Load API keys from environment
     */
    private function loadApiKeys(): void
    {
        $this->assemblyaiApiKey = $_ENV['ASSEMBLYAI_API_KEY'];
        $this->openaiApiKey = $_ENV['OPENAI_API_KEY'];
        $this->openaiModel = $_ENV['OPENAI_MODEL'] ?? self::DEFAULT_OPENAI_MODEL;
    }
    
    /**
     * Extract and validate the recording URL from CRM data
     * 
     * @param array $crmData
     * @return string
     * @throws \InvalidArgumentException
     */
    private function extractRecordingUrl(array $crmData): string
    {
        if (!isset($crmData['recordingPath']) || empty($crmData['recordingPath'])) {
            throw new \InvalidArgumentException('recordingPath field is missing or empty in CRM data');
        }
        
        $recordingPath = $crmData['recordingPath'];
        $this->logger->info("Extracted recording URL: {$recordingPath}");
        
        return $recordingPath;
    }
    
    /**
     * Transcribe audio using AssemblyAI with Hebrew language support
     * 
     * @param string $audioUrl
     * @return array
     */
    public function transcribeAudio(string $audioUrl): array
    {
        try {
            $this->logger->info("Starting transcription for audio URL: {$audioUrl}");
            
            // Submit transcription job
            $transcriptId = $this->submitTranscriptionJob($audioUrl);
            
            // Poll for completion
            $result = $this->pollTranscriptionResult($transcriptId);
            
            if ($result['status'] === 'completed') {
                $this->logger->info("Transcription completed successfully. ID: {$transcriptId}");
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error("Transcription error: " . $e->getMessage());
            return [
                'status' => 'error',
                'transcript_id' => null,
                'text' => null,
                'language_code' => self::HEBREW_LANGUAGE_CODE,
                'confidence' => null,
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Submit transcription job to AssemblyAI
     * 
     * @param string $audioUrl
     * @return string
     * @throws \Exception
     */
    private function submitTranscriptionJob(string $audioUrl): string
    {
        // First try to upload the file directly if it's accessible
        $uploadUrl = $this->tryUploadAudioFile($audioUrl);
        
        $payload = [
            'audio_url' => $uploadUrl ?: $audioUrl,
            'language_code' => self::HEBREW_LANGUAGE_CODE
            // Note: sentiment_analysis and auto_highlights not available for Hebrew
        ];
        
        $this->logger->info("Submitting transcription job", [
            'original_url' => $audioUrl,
            'final_url' => $uploadUrl ?: $audioUrl,
            'upload_method' => $uploadUrl ? 'direct_upload' : 'url_reference'
        ]);
        
        $response = $this->httpClient->post('https://api.assemblyai.com/v2/transcript', [
            'headers' => [
                'Authorization' => $this->assemblyaiApiKey
            ],
            'json' => $payload
        ]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        
        if (!isset($data['id'])) {
            $errorDetails = json_encode($data, JSON_PRETTY_PRINT);
            $this->logger->error("Failed to submit transcription job", [
                'response' => $errorDetails
            ]);
            throw new \Exception('Failed to get transcript ID from AssemblyAI: ' . ($data['error'] ?? 'Unknown error'));
        }
        
        return $data['id'];
    }
    
    /**
     * Try to upload audio file directly to AssemblyAI if URL is not accessible
     * 
     * @param string $audioUrl
     * @return string|null Returns upload URL if successful, null if failed
     */
    private function tryUploadAudioFile(string $audioUrl): ?string
    {
        // First try to proxy the file if it's a private URL
        $proxyUrl = $this->tryProxyAudioFile($audioUrl);
        if ($proxyUrl) {
            return $proxyUrl;
        }
        
        // Fallback to direct upload
        return $this->tryDirectUpload($audioUrl);
    }
    
    /**
     * Try to proxy audio file for private URLs
     * 
     * @param string $audioUrl
     * @return string|null
     */
    private function tryProxyAudioFile(string $audioUrl): ?string
    {
        try {
            // Check if this looks like a private URL
            if (strpos($audioUrl, 'master.ippbx.co.il') !== false || 
                strpos($audioUrl, 'localhost') !== false ||
                strpos($audioUrl, '192.168.') !== false ||
                strpos($audioUrl, '10.') !== false) {
                
                $this->logger->info("Detected private URL, attempting to proxy", ['url' => $audioUrl]);
                
                $proxy = new AudioProxy();
                $publicUrl = $proxy->proxyAudioFile($audioUrl);
                
                if ($publicUrl) {
                    $this->logger->info("Successfully proxied audio file", [
                        'private_url' => $audioUrl,
                        'public_url' => $publicUrl
                    ]);
                    return $publicUrl;
                }
            }
            
            return null;
            
        } catch (\Exception $e) {
            $this->logger->warning("Failed to proxy audio file", [
                'url' => $audioUrl,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Try direct upload to AssemblyAI
     * 
     * @param string $audioUrl
     * @return string|null
     */
    private function tryDirectUpload(string $audioUrl): ?string
    {
        try {
            $this->logger->info("Attempting to download and upload audio file", ['url' => $audioUrl]);
            
            // Download the file
            $audioResponse = $this->httpClient->get($audioUrl, [
                'timeout' => 30,
                'stream' => true
            ]);
            
            if ($audioResponse->getStatusCode() !== 200) {
                $this->logger->warning("Failed to download audio file", [
                    'url' => $audioUrl,
                    'status_code' => $audioResponse->getStatusCode()
                ]);
                return null;
            }
            
            // Get upload URL from AssemblyAI
            $uploadResponse = $this->httpClient->post('https://api.assemblyai.com/v2/upload', [
                'headers' => [
                    'Authorization' => $this->assemblyaiApiKey,
                    'Transfer-Encoding' => 'chunked'
                ],
                'body' => $audioResponse->getBody()
            ]);
            
            $uploadData = json_decode($uploadResponse->getBody()->getContents(), true);
            
            if (isset($uploadData['upload_url'])) {
                $this->logger->info("Successfully uploaded audio file to AssemblyAI", [
                    'upload_url' => $uploadData['upload_url']
                ]);
                return $uploadData['upload_url'];
            }
            
            $this->logger->warning("Failed to get upload URL from AssemblyAI", [
                'response' => json_encode($uploadData)
            ]);
            return null;
            
        } catch (\Exception $e) {
            $this->logger->warning("Failed to upload audio file, falling back to URL", [
                'url' => $audioUrl,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Poll for transcription completion
     * 
     * @param string $transcriptId
     * @return array
     * @throws \Exception
     */
    private function pollTranscriptionResult(string $transcriptId): array
    {
        $startTime = time();
        
        while (true) {
            $response = $this->httpClient->get("https://api.assemblyai.com/v2/transcript/{$transcriptId}", [
                'headers' => [
                    'Authorization' => $this->assemblyaiApiKey
                ]
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);
            $status = $data['status'] ?? 'unknown';
            
            $this->logger->info("Transcription status: {$status}");
            
            if ($status === 'completed') {
                return [
                    'status' => 'completed',
                    'transcript_id' => $transcriptId,
                    'text' => $data['text'] ?? '',
                    'language_code' => self::HEBREW_LANGUAGE_CODE,
                    'confidence' => $data['confidence'] ?? null,
                    'error_message' => null
                ];
            }
            
            if ($status === 'error') {
                $errorMessage = $data['error'] ?? 'Transcription failed';
                $errorDetails = json_encode($data, JSON_PRETTY_PRINT);
                
                $this->logger->error("AssemblyAI transcription failed", [
                    'transcript_id' => $transcriptId,
                    'error_message' => $errorMessage,
                    'full_response' => $errorDetails
                ]);
                
                return [
                    'status' => 'error',
                    'transcript_id' => $transcriptId,
                    'text' => null,
                    'language_code' => self::HEBREW_LANGUAGE_CODE,
                    'confidence' => null,
                    'error_message' => $errorMessage,
                    'error_details' => $data
                ];
            }
            
            if (time() - $startTime > self::TRANSCRIPTION_TIMEOUT_SECONDS) {
                $this->logger->error("AssemblyAI transcription timeout", [
                    'transcript_id' => $transcriptId,
                    'timeout_seconds' => self::TRANSCRIPTION_TIMEOUT_SECONDS,
                    'elapsed_time' => time() - $startTime
                ]);
                
                return [
                    'status' => 'error',
                    'transcript_id' => $transcriptId,
                    'text' => null,
                    'language_code' => self::HEBREW_LANGUAGE_CODE,
                    'confidence' => null,
                    'error_message' => 'Transcription timeout exceeded',
                    'error_details' => [
                        'timeout_seconds' => self::TRANSCRIPTION_TIMEOUT_SECONDS,
                        'elapsed_time' => time() - $startTime
                    ]
                ];
            }
            
            sleep(self::POLLING_INTERVAL_SECONDS);
        }
    }
    
    /**
     * Analyze transcript using OpenAI GPT-4 for structured insights
     * 
     * @param string $transcriptText
     * @return array
     */
    public function analyzeTranscript(string $transcriptText): array
    {
        try {
            $this->logger->info("Starting AI analysis of transcript");
            
            // Get system prompt from environment variable with fallback
            $systemPrompt = $_ENV['AI_SYSTEM_PROMPT'] ?? "You are an expert call analyst for a business. Your task is to analyze the following call transcript and provide a structured summary in a strict JSON format. The call is in Hebrew.

Based on the transcript, you must extract the following information:
1. **summary**: A concise, neutral summary of the call's purpose and outcome.
2. **call_sentiment**: The overall sentiment of the customer. Options are: \"Positive\", \"Neutral\", \"Negative\".
3. **main_topics**: A list of the key topics discussed during the call.
4. **action_items**: A list of specific, actionable tasks for the business or agent, including who is responsible if mentioned. If none, provide an empty list.
5. **customer_satisfaction_score**: An estimated customer satisfaction score on a scale of 1 to 10, where 1 is \"Very Dissatisfied\" and 10 is \"Very Satisfied\".

Provide your response as a single JSON object ONLY. Do not include any introductory text, explanations, or markdown formatting like ```json.";
            
            $payload = [
                'model' => $this->openaiModel,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $transcriptText]
                ],
                'temperature' => 0.3,
                'max_tokens' => 1000
            ];
            
            $response = $this->httpClient->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->openaiApiKey
                ],
                'json' => $payload
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);
            $responseText = trim($data['choices'][0]['message']['content'] ?? '');
            
            // Clean up response if it contains markdown formatting
            if (str_starts_with($responseText, '```json')) {
                $responseText = substr($responseText, 7);
            }
            if (str_ends_with($responseText, '```')) {
                $responseText = substr($responseText, 0, -3);
            }
            
            $analysisData = json_decode($responseText, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON response from AI: ' . json_last_error_msg());
            }
            
            $this->logger->info("AI analysis completed successfully");
            
            return [
                'status' => 'completed',
                'summary' => $analysisData['summary'] ?? null,
                'call_sentiment' => $analysisData['call_sentiment'] ?? null,
                'main_topics' => $analysisData['main_topics'] ?? [],
                'action_items' => $analysisData['action_items'] ?? [],
                'customer_satisfaction_score' => $analysisData['customer_satisfaction_score'] ?? null,
                'error_message' => null
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("AI analysis error: " . $e->getMessage());
            return [
                'status' => 'error',
                'summary' => null,
                'call_sentiment' => null,
                'main_topics' => null,
                'action_items' => null,
                'customer_satisfaction_score' => null,
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Main workflow function that orchestrates the entire pipeline
     * 
     * @param array $crmData
     * @return array
     */
    public function processCall(array $crmData): array
    {
        $callId = $crmData['callid'] ?? 'unknown';
        $this->logger->info("Starting call processing for call ID: {$callId}");
        
        try {
            // Stage 1: Extract recording URL
            $recordingUrl = $this->extractRecordingUrl($crmData);
            
            // Stage 2: Transcribe audio
            $transcriptionResult = $this->transcribeAudio($recordingUrl);
            
            // Stage 3: Analyze transcript (only if transcription was successful)
            $aiAnalysisResult = [
                'status' => 'error',
                'summary' => null,
                'call_sentiment' => null,
                'main_topics' => null,
                'action_items' => null,
                'customer_satisfaction_score' => null,
                'error_message' => 'No transcript available'
            ];
            
            if ($transcriptionResult['status'] === 'completed' && !empty($transcriptionResult['text'])) {
                $aiAnalysisResult = $this->analyzeTranscript($transcriptionResult['text']);
            }
            
            // Stage 4: Store call data in database
            $this->storeCallData($crmData, $transcriptionResult, $aiAnalysisResult);
            
            // Stage 5: Create and send survey (if enabled and customer phone available)
            $surveyResult = $this->createAndSendSurvey($crmData, $callId);
            
            // Stage 6: Consolidate results
            $finalResult = [
                'call_details' => $crmData,
                'transcription_result' => $transcriptionResult,
                'ai_analysis' => $aiAnalysisResult,
                'survey_result' => $surveyResult
            ];
            
            $this->logger->info("Call processing pipeline completed successfully");
            return $finalResult;
            
        } catch (\Exception $e) {
            $this->logger->error("Pipeline error: " . $e->getMessage());
            return [
                'call_details' => $crmData,
                'transcription_result' => [
                    'status' => 'error',
                    'transcript_id' => null,
                    'text' => null,
                    'language_code' => self::HEBREW_LANGUAGE_CODE,
                    'confidence' => null,
                    'error_message' => $e->getMessage()
                ],
                'ai_analysis' => [
                    'status' => 'error',
                    'summary' => null,
                    'call_sentiment' => null,
                    'main_topics' => null,
                    'action_items' => null,
                    'customer_satisfaction_score' => null,
                    'error_message' => 'Pipeline failed before analysis'
                ],
                'survey_result' => [
                    'status' => 'error',
                    'error_message' => 'Pipeline failed before survey creation'
                ]
            ];
        }
    }
    
    /**
     * Store call data in database
     * 
     * @param array $crmData
     * @param array $transcriptionResult
     * @param array $aiAnalysisResult
     * @return bool
     */
    private function storeCallData(array $crmData, array $transcriptionResult, array $aiAnalysisResult): bool
    {
        try {
            $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
            $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $stmt = $pdo->prepare("
                INSERT INTO calls (
                    call_id, recording_url, start_date, call_direction, duration,
                    agent_name, customer_phone, transcription_text, transcription_confidence,
                    ai_summary, ai_sentiment, ai_topics, ai_action_items, ai_satisfaction_score,
                    processing_status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                    recording_url = VALUES(recording_url),
                    start_date = VALUES(start_date),
                    call_direction = VALUES(call_direction),
                    duration = VALUES(duration),
                    agent_name = VALUES(agent_name),
                    customer_phone = VALUES(customer_phone),
                    transcription_text = VALUES(transcription_text),
                    transcription_confidence = VALUES(transcription_confidence),
                    ai_summary = VALUES(ai_summary),
                    ai_sentiment = VALUES(ai_sentiment),
                    ai_topics = VALUES(ai_topics),
                    ai_action_items = VALUES(ai_action_items),
                    ai_satisfaction_score = VALUES(ai_satisfaction_score),
                    processing_status = VALUES(processing_status),
                    updated_at = NOW()
            ");
            
            $processingStatus = $aiAnalysisResult['status'] === 'completed' ? 'completed' : 'failed';
            
            // Extract customer phone and agent info
            $customerPhone = $this->extractCustomerPhone($crmData);
            $agentInfo = $this->extractAgentInfo($crmData);
            
            return $stmt->execute([
                $crmData['callid'] ?? 'unknown',
                $crmData['recordingPath'] ?? null,
                $crmData['start_date'] ?? null,
                $crmData['call_direction'] ?? null,
                $crmData['call_sec'] ?? null,
                $agentInfo['name'] ?? null,
                $customerPhone,
                $transcriptionResult['text'] ?? null,
                $transcriptionResult['confidence'] ?? null,
                $aiAnalysisResult['summary'] ?? null,
                $aiAnalysisResult['call_sentiment'] ?? null,
                json_encode($aiAnalysisResult['main_topics'] ?? []),
                json_encode($aiAnalysisResult['action_items'] ?? []),
                $aiAnalysisResult['customer_satisfaction_score'] ?? null,
                $processingStatus
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to store call data", [
                'call_id' => $crmData['callid'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Create and send survey for the call
     * 
     * @param array $crmData
     * @param string $callId
     * @return array
     */
    private function createAndSendSurvey(array $crmData, string $callId): array
    {
        try {
            // Check if survey is enabled
            if (($_ENV['SURVEY_ENABLED'] ?? 'true') !== 'true') {
                return [
                    'status' => 'disabled',
                    'message' => 'Survey feature is disabled'
                ];
            }
            
            // Extract customer phone number based on call direction
            $customerPhone = $this->extractCustomerPhone($crmData);
            if (empty($customerPhone)) {
                return [
                    'status' => 'no_phone',
                    'message' => 'No customer phone number available'
                ];
            }
            
            // Create survey
            $surveyService = new \App\SurveyService();
            $surveyResult = $surveyService->createSurvey($callId, $customerPhone);
            
            if ($surveyResult['status'] !== 'created') {
                return [
                    'status' => 'survey_creation_failed',
                    'error_message' => $surveyResult['error_message']
                ];
            }
            
            // Send WhatsApp message
            $whatsappService = new \App\WhatsAppService();
            $whatsappResult = $whatsappService->sendSurveyMessage(
                $customerPhone,
                $surveyResult['survey_url'],
                $callId
            );
            
            // Update survey with WhatsApp status
            if ($whatsappResult['status'] === 'sent') {
                $surveyService->updateWhatsAppStatus(
                    $surveyResult['survey_id'],
                    $whatsappResult['message_id'],
                    'sent'
                );
            }
            
            return [
                'status' => 'success',
                'survey_id' => $surveyResult['survey_id'],
                'survey_url' => $surveyResult['survey_url'],
                'whatsapp_status' => $whatsappResult['status'],
                'whatsapp_message_id' => $whatsappResult['message_id'] ?? null,
                'error_message' => $whatsappResult['error_message'] ?? null
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to create and send survey", [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'error',
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Extract customer phone number based on call direction
     * 
     * @param array $crmData
     * @return string|null
     */
    public function extractCustomerPhone(array $crmData): ?string
    {
        $callDirection = strtoupper($crmData['call_direction'] ?? '');
        
        $this->logger->info("Extracting customer phone for call direction", [
            'call_direction' => $callDirection,
            'caller' => $crmData['caller'] ?? 'N/A',
            'callee' => $crmData['callee'] ?? 'N/A',
            'outbound_caller_id' => $crmData['outbound_caller_id'] ?? 'N/A'
        ]);
        
        // For OUTBOUND calls: customer is the callee
        if ($callDirection === 'OUTBOUND') {
            $customerPhone = $crmData['callee'] ?? null;
            
            // If callee is empty, try to extract from callee_name
            if (empty($customerPhone) && !empty($crmData['callee_name'])) {
                $customerPhone = $this->extractPhoneFromName($crmData['callee_name']);
            }
            
            $this->logger->info("OUTBOUND call - customer phone extracted", [
                'customer_phone' => $customerPhone,
                'source' => 'callee'
            ]);
            
            return $customerPhone;
        }
        
        // For INBOUND calls: customer is the caller
        if ($callDirection === 'INBOUND') {
            $customerPhone = $crmData['caller'] ?? null;
            
            // If caller is empty, try to extract from caller_name
            if (empty($customerPhone) && !empty($crmData['caller_name'])) {
                $customerPhone = $this->extractPhoneFromName($crmData['caller_name']);
            }
            
            $this->logger->info("INBOUND call - customer phone extracted", [
                'customer_phone' => $customerPhone,
                'source' => 'caller'
            ]);
            
            return $customerPhone;
        }
        
        // Fallback: try to use customer_phone field if provided
        if (!empty($crmData['customer_phone'])) {
            $this->logger->info("Using fallback customer_phone field", [
                'customer_phone' => $crmData['customer_phone']
            ]);
            return $crmData['customer_phone'];
        }
        
        $this->logger->warning("Could not determine customer phone number", [
            'call_direction' => $callDirection,
            'available_fields' => array_keys($crmData)
        ]);
        
        return null;
    }
    
    /**
     * Extract phone number from name field (e.g., "esim 0526235589")
     * 
     * @param string $name
     * @return string|null
     */
    public function extractPhoneFromName(string $name): ?string
    {
        // Look for phone number patterns in the name
        $patterns = [
            '/\b(\+?972\d{8,9})\b/',  // Israeli numbers with +972
            '/\b(\+?972\d{2}\d{7})\b/', // Israeli numbers with area code
            '/\b(\d{10,11})\b/',      // General 10-11 digit numbers
            '/\b(\d{9})\b/'           // 9 digit numbers
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $name, $matches)) {
                $phone = $matches[1];
                
                // Ensure it starts with country code for WhatsApp
                if (!str_starts_with($phone, '+')) {
                    // If it's a 9-digit number, assume it's Israeli and add +972
                    if (strlen($phone) === 9) {
                        $phone = '+972' . $phone;
                    }
                    // If it's a 10-digit number starting with 0, replace with +972
                    elseif (strlen($phone) === 10 && str_starts_with($phone, '0')) {
                        $phone = '+972' . substr($phone, 1);
                    }
                    // If it's already 11 digits, add +
                    elseif (strlen($phone) === 11) {
                        $phone = '+' . $phone;
                    }
                }
                
                return $phone;
            }
        }
        
        return null;
    }
    
    /**
     * Extract agent information from call data
     * 
     * @param array $crmData
     * @return array
     */
    public function extractAgentInfo(array $crmData): array
    {
        $callDirection = strtoupper($crmData['call_direction'] ?? '');
        
        // For OUTBOUND calls: agent is the caller
        if ($callDirection === 'OUTBOUND') {
            $agentName = $crmData['caller_name'] ?? null;
            $agentPhone = $crmData['caller'] ?? null;
            
            // If caller_name contains phone number, extract just the name
            if ($agentName && preg_match('/\d/', $agentName)) {
                $agentName = trim(preg_replace('/\d+/', '', $agentName));
            }
            
            return [
                'name' => $agentName ?: 'Agent',
                'phone' => $agentPhone
            ];
        }
        
        // For INBOUND calls: agent is the callee
        if ($callDirection === 'INBOUND') {
            $agentName = $crmData['callee_name'] ?? null;
            $agentPhone = $crmData['callee'] ?? null;
            
            return [
                'name' => $agentName ?: 'Agent',
                'phone' => $agentPhone
            ];
        }
        
        // Fallback
        return [
            'name' => $crmData['agent_name'] ?? 'Agent',
            'phone' => null
        ];
    }
} 