<?php

namespace App;

use PDO;
use Ramsey\Uuid\Uuid;
use Monolog\Logger;

class SurveyService
{
    private PDO $pdo;
    private Logger $logger;
    private string $baseUrl;
    private int $expiryHours;
    
    public function __construct()
    {
        $this->setupLogger();
        $this->setupDatabase();
        $this->loadConfiguration();
    }
    
    private function setupLogger(): void
    {
        $this->logger = new Logger('survey_service');
        $this->logger->pushHandler(new \Monolog\Handler\StreamHandler(
            'logs/survey_service.log',
            Logger::INFO
        ));
    }
    
    private function setupDatabase(): void
    {
        $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
        $this->pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS']);
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    private function loadConfiguration(): void
    {
        $this->baseUrl = $_ENV['SURVEY_BASE_URL'] ?? 'https://your-domain.com/survey';
        $this->expiryHours = 72; // 3 days default
    }
    
    /**
     * Create a new survey for a call
     * 
     * @param string $callId
     * @param string $customerPhone
     * @return array
     */
    public function createSurvey(string $callId, string $customerPhone): array
    {
        try {
            $this->logger->info("Creating survey for call", [
                'call_id' => $callId,
                'customer_phone' => $customerPhone
            ]);
            
            // Generate unique survey ID
            $surveyId = Uuid::uuid4()->toString();
            
            // Create survey URL
            $surveyUrl = $this->baseUrl . '/' . $surveyId;
            
            // Set expiry date
            $expiresAt = date('Y-m-d H:i:s', strtotime("+{$this->expiryHours} hours"));
            
            // Insert into database
            $stmt = $this->pdo->prepare("
                INSERT INTO surveys (
                    survey_id, call_id, customer_phone, survey_url, 
                    survey_status, expires_at, created_at
                ) VALUES (?, ?, ?, ?, 'pending', ?, NOW())
            ");
            
            $stmt->execute([
                $surveyId,
                $callId,
                $customerPhone,
                $surveyUrl,
                $expiresAt
            ]);
            
            $this->logger->info("Survey created successfully", [
                'survey_id' => $surveyId,
                'survey_url' => $surveyUrl,
                'expires_at' => $expiresAt
            ]);
            
            return [
                'status' => 'created',
                'survey_id' => $surveyId,
                'survey_url' => $surveyUrl,
                'expires_at' => $expiresAt,
                'error_message' => null
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to create survey", [
                'call_id' => $callId,
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'error',
                'survey_id' => null,
                'survey_url' => null,
                'expires_at' => null,
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get survey by ID
     * 
     * @param string $surveyId
     * @return array|null
     */
    public function getSurvey(string $surveyId): ?array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT s.*, c.call_id, c.customer_phone, c.agent_name, c.start_date
                FROM surveys s
                JOIN calls c ON s.call_id = c.call_id
                WHERE s.survey_id = ?
            ");
            
            $stmt->execute([$surveyId]);
            $survey = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($survey) {
                // Check if survey is expired
                if (strtotime($survey['expires_at']) < time()) {
                    $this->updateSurveyStatus($surveyId, 'expired');
                    $survey['survey_status'] = 'expired';
                }
                
                return $survey;
            }
            
            return null;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get survey", [
                'survey_id' => $surveyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Submit survey response
     * 
     * @param string $surveyId
     * @param array $responses
     * @return array
     */
    public function submitSurvey(string $surveyId, array $responses): array
    {
        try {
            $this->logger->info("Submitting survey response", [
                'survey_id' => $surveyId,
                'responses' => $responses
            ]);
            
            // Get survey
            $survey = $this->getSurvey($surveyId);
            if (!$survey) {
                throw new \Exception("Survey not found");
            }
            
            if ($survey['survey_status'] !== 'pending') {
                throw new \Exception("Survey is not available for submission");
            }
            
            // Extract satisfaction score
            $satisfactionScore = $this->extractSatisfactionScore($responses);
            
            // Update survey with responses
            $stmt = $this->pdo->prepare("
                UPDATE surveys 
                SET survey_status = 'completed',
                    satisfaction_score = ?,
                    survey_responses = ?,
                    submitted_at = NOW(),
                    updated_at = NOW()
                WHERE survey_id = ?
            ");
            
            $stmt->execute([
                $satisfactionScore,
                json_encode($responses),
                $surveyId
            ]);
            
            // Create analytics entry
            $this->createAnalyticsEntry($survey['call_id'], $surveyId, $responses, $satisfactionScore);
            
            $this->logger->info("Survey submitted successfully", [
                'survey_id' => $surveyId,
                'satisfaction_score' => $satisfactionScore
            ]);
            
            return [
                'status' => 'submitted',
                'survey_id' => $surveyId,
                'satisfaction_score' => $satisfactionScore,
                'error_message' => null
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to submit survey", [
                'survey_id' => $surveyId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'error',
                'survey_id' => $surveyId,
                'satisfaction_score' => null,
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update survey status
     * 
     * @param string $surveyId
     * @param string $status
     * @return bool
     */
    public function updateSurveyStatus(string $surveyId, string $status): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE surveys 
                SET survey_status = ?, updated_at = NOW()
                WHERE survey_id = ?
            ");
            
            return $stmt->execute([$status, $surveyId]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to update survey status", [
                'survey_id' => $surveyId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Update WhatsApp message status
     * 
     * @param string $surveyId
     * @param string $messageId
     * @param string $status
     * @return bool
     */
    public function updateWhatsAppStatus(string $surveyId, string $messageId, string $status): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE surveys 
                SET whatsapp_message_id = ?, 
                    whatsapp_status = ?, 
                    updated_at = NOW()
                WHERE survey_id = ?
            ");
            
            return $stmt->execute([$messageId, $status, $surveyId]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to update WhatsApp status", [
                'survey_id' => $surveyId,
                'message_id' => $messageId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Extract satisfaction score from responses
     * 
     * @param array $responses
     * @return int|null
     */
    private function extractSatisfactionScore(array $responses): ?int
    {
        // Look for satisfaction score in various possible fields
        $scoreFields = ['satisfaction', 'satisfaction_score', 'rating', 'score', 'overall_rating'];
        
        foreach ($scoreFields as $field) {
            if (isset($responses[$field])) {
                $score = (int) $responses[$field];
                if ($score >= 1 && $score <= 10) {
                    return $score;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Create analytics entry
     * 
     * @param string $callId
     * @param string $surveyId
     * @param array $responses
     * @param int|null $satisfactionScore
     * @return bool
     */
    private function createAnalyticsEntry(string $callId, string $surveyId, array $responses, ?int $satisfactionScore): bool
    {
        try {
            // Get call data
            $stmt = $this->pdo->prepare("
                SELECT customer_phone, ai_sentiment, ai_topics, ai_action_items, ai_satisfaction_score
                FROM calls 
                WHERE call_id = ?
            ");
            
            $stmt->execute([$callId]);
            $callData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$callData) {
                return false;
            }
            
            // Calculate combined score
            $combinedScore = $this->calculateCombinedScore(
                $callData['ai_satisfaction_score'],
                $satisfactionScore
            );
            
            // Generate insights
            $insights = $this->generateInsights($callData, $responses);
            
            // Insert analytics entry
            $stmt = $this->pdo->prepare("
                INSERT INTO analytics (
                    call_id, survey_id, customer_phone, call_satisfaction_score,
                    survey_satisfaction_score, ai_sentiment, ai_topics, ai_action_items,
                    survey_responses, combined_score, insights, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            return $stmt->execute([
                $callId,
                $surveyId,
                $callData['customer_phone'],
                $callData['ai_satisfaction_score'],
                $satisfactionScore,
                $callData['ai_sentiment'],
                $callData['ai_topics'],
                $callData['ai_action_items'],
                json_encode($responses),
                $combinedScore,
                $insights
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to create analytics entry", [
                'call_id' => $callId,
                'survey_id' => $surveyId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Calculate combined satisfaction score
     * 
     * @param int|null $aiScore
     * @param int|null $surveyScore
     * @return float|null
     */
    private function calculateCombinedScore(?int $aiScore, ?int $surveyScore): ?float
    {
        if ($aiScore === null && $surveyScore === null) {
            return null;
        }
        
        if ($aiScore === null) {
            return (float) $surveyScore;
        }
        
        if ($surveyScore === null) {
            return (float) $aiScore;
        }
        
        // Weight: 40% AI, 60% Survey (survey is more reliable)
        return ($aiScore * 0.4) + ($surveyScore * 0.6);
    }
    
    /**
     * Generate insights from call and survey data
     * 
     * @param array $callData
     * @param array $responses
     * @return string
     */
    private function generateInsights(array $callData, array $responses): string
    {
        $insights = [];
        
        // Compare AI vs Survey satisfaction
        if (isset($callData['ai_satisfaction_score']) && isset($responses['satisfaction'])) {
            $aiScore = $callData['ai_satisfaction_score'];
            $surveyScore = (int) $responses['satisfaction'];
            $difference = abs($aiScore - $surveyScore);
            
            if ($difference <= 2) {
                $insights[] = "AI analysis accurately predicted customer satisfaction";
            } else {
                $insights[] = "Significant difference between AI prediction and actual satisfaction";
            }
        }
        
        // Sentiment analysis
        if (isset($callData['ai_sentiment'])) {
            $insights[] = "Call sentiment: " . $callData['ai_sentiment'];
        }
        
        // Key topics
        if (isset($callData['ai_topics'])) {
            $topics = json_decode($callData['ai_topics'], true);
            if (is_array($topics) && !empty($topics)) {
                $insights[] = "Main topics: " . implode(', ', $topics);
            }
        }
        
        return implode('; ', $insights);
    }
    
    /**
     * Get survey statistics
     * 
     * @return array
     */
    public function getStatistics(): array
    {
        try {
            $stats = [];
            
            // Total surveys
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM surveys");
            $stats['total_surveys'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Completed surveys
            $stmt = $this->pdo->query("SELECT COUNT(*) as completed FROM surveys WHERE survey_status = 'completed'");
            $stats['completed_surveys'] = $stmt->fetch(PDO::FETCH_ASSOC)['completed'];
            
            // Average satisfaction score
            $stmt = $this->pdo->query("SELECT AVG(satisfaction_score) as avg_score FROM surveys WHERE satisfaction_score IS NOT NULL");
            $stats['average_satisfaction'] = round($stmt->fetch(PDO::FETCH_ASSOC)['avg_score'], 2);
            
            // Response rate
            $stats['response_rate'] = $stats['total_surveys'] > 0 
                ? round(($stats['completed_surveys'] / $stats['total_surveys']) * 100, 2)
                : 0;
            
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get statistics", ['error' => $e->getMessage()]);
            return [];
        }
    }
} 