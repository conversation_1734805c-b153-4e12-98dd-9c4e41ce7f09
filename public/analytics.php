<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\SurveyService;
use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

$surveyService = new SurveyService();

// Get statistics
$stats = $surveyService->getStatistics();

// Get recent analytics data
try {
    $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get recent analytics
    $stmt = $pdo->query("
        SELECT a.*, c.start_date, c.agent_name, c.call_direction
        FROM analytics a
        JOIN calls c ON a.call_id = c.call_id
        ORDER BY a.created_at DESC
        LIMIT 50
    ");
    $recentAnalytics = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get satisfaction trends
    $stmt = $pdo->query("
        SELECT 
            DATE(created_at) as date,
            AVG(combined_score) as avg_score,
            COUNT(*) as total_calls
        FROM analytics 
        WHERE combined_score IS NOT NULL
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
    ");
    $satisfactionTrends = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $recentAnalytics = [];
    $satisfactionTrends = [];
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - AI Call Summarizer</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .chart-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
        }
        
        .analytics-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #667eea;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .score-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .score-high {
            background: #d4edda;
            color: #155724;
        }
        
        .score-medium {
            background: #fff3cd;
            color: #856404;
        }
        
        .score-low {
            background: #f8d7da;
            color: #721c24;
        }
        
        .sentiment-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .sentiment-positive {
            background: #d4edda;
            color: #155724;
        }
        
        .sentiment-neutral {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .sentiment-negative {
            background: #f8d7da;
            color: #721c24;
        }
        
        .insights {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
            font-size: 0.9em;
            color: #1976d2;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            th, td {
                padding: 10px 5px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Analytics Dashboard</h1>
        <p>AI Call Summarizer & Survey Analytics</p>
    </div>
    
    <div class="container">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['total_surveys'] ?? 0 ?></div>
                <div class="stat-label">Total Surveys Sent</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= $stats['completed_surveys'] ?? 0 ?></div>
                <div class="stat-label">Completed Surveys</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= $stats['response_rate'] ?? 0 ?>%</div>
                <div class="stat-label">Response Rate</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= $stats['average_satisfaction'] ?? 0 ?></div>
                <div class="stat-label">Average Satisfaction (1-10)</div>
            </div>
        </div>
        
        <!-- Satisfaction Trends Chart -->
        <?php if (!empty($satisfactionTrends)): ?>
        <div class="chart-container">
            <div class="chart-title">Satisfaction Trends (Last 30 Days)</div>
            <canvas id="satisfactionChart" width="400" height="200"></canvas>
        </div>
        <?php endif; ?>
        
        <!-- Recent Analytics Table -->
        <div class="analytics-table">
            <div class="table-header">
                Recent Call Analytics
            </div>
            
            <?php if (!empty($recentAnalytics)): ?>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Call ID</th>
                        <th>Agent</th>
                        <th>AI Sentiment</th>
                        <th>AI Score</th>
                        <th>Survey Score</th>
                        <th>Combined Score</th>
                        <th>Insights</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentAnalytics as $analytics): ?>
                    <tr>
                        <td><?= date('d/m/Y H:i', strtotime($analytics['start_date'])) ?></td>
                        <td><?= htmlspecialchars($analytics['call_id']) ?></td>
                        <td><?= htmlspecialchars($analytics['agent_name'] ?? 'N/A') ?></td>
                        <td>
                            <?php if ($analytics['ai_sentiment']): ?>
                                <span class="sentiment-badge sentiment-<?= strtolower($analytics['ai_sentiment']) ?>">
                                    <?= htmlspecialchars($analytics['ai_sentiment']) ?>
                                </span>
                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($analytics['call_satisfaction_score']): ?>
                                <span class="score-badge score-<?= $analytics['call_satisfaction_score'] >= 7 ? 'high' : ($analytics['call_satisfaction_score'] >= 4 ? 'medium' : 'low') ?>">
                                    <?= $analytics['call_satisfaction_score'] ?>/10
                                </span>
                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($analytics['survey_satisfaction_score']): ?>
                                <span class="score-badge score-<?= $analytics['survey_satisfaction_score'] >= 7 ? 'high' : ($analytics['survey_satisfaction_score'] >= 4 ? 'medium' : 'low') ?>">
                                    <?= $analytics['survey_satisfaction_score'] ?>/10
                                </span>
                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($analytics['combined_score']): ?>
                                <span class="score-badge score-<?= $analytics['combined_score'] >= 7 ? 'high' : ($analytics['combined_score'] >= 4 ? 'medium' : 'low') ?>">
                                    <?= round($analytics['combined_score'], 1) ?>/10
                                </span>
                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($analytics['insights']): ?>
                                <div class="insights">
                                    <?= htmlspecialchars(substr($analytics['insights'], 0, 100)) ?>...
                                </div>
                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="no-data">
                No analytics data available yet. Process some calls to see results here.
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (!empty($satisfactionTrends)): ?>
    <script>
        // Satisfaction Trends Chart
        const ctx = document.getElementById('satisfactionChart').getContext('2d');
        const chartData = <?= json_encode($satisfactionTrends) ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.map(item => item.date).reverse(),
                datasets: [{
                    label: 'Average Satisfaction Score',
                    data: chartData.map(item => item.avg_score).reverse(),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Daily Average Satisfaction Score'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 10,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    </script>
    <?php endif; ?>
</body>
</html> 