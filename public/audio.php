<?php
/**
 * Simple Audio File Server
 * Serves audio files from temp directory for AssemblyAI access
 */

// Security: Only allow access to audio files
$requestedFile = $_GET['file'] ?? '';
if (empty($requestedFile) || !preg_match('/^[a-f0-9_]+\.wav$/', $requestedFile)) {
    http_response_code(400);
    die('Invalid file request');
}

$tempDir = sys_get_temp_dir() . '/audio_proxy/';
$filepath = $tempDir . $requestedFile;

if (!file_exists($filepath)) {
    http_response_code(404);
    die('File not found');
}

// Set headers for audio file
header('Content-Type: audio/wav');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Stream the file
readfile($filepath); 