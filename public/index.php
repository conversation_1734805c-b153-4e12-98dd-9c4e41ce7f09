<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Slim\Factory\AppFactory;
use Slim\Psr7\Request;
use Slim\Psr7\Response;
use App\CallSummarizer;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Create Slim app
$app = AppFactory::create();

// Add error middleware
$app->addErrorMiddleware(true, true, true);

// Initialize CallSummarizer
$summarizer = null;

try {
    $summarizer = new CallSummarizer();
} catch (Exception $e) {
    error_log("Failed to initialize CallSummarizer: " . $e->getMessage());
}

/**
 * Health check endpoint
 */
$app->get('/health', function (Request $request, Response $response) {
    $data = [
        'status' => 'healthy',
        'service' => 'AI Call Summarizer',
        'version' => '1.0.0',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
    return $response->withHeader('Content-Type', 'application/json');
});

/**
 * Service status endpoint
 */
$app->get('/status', function (Request $request, Response $response) use ($summarizer) {
    if ($summarizer === null) {
        $data = [
            'status' => 'error',
            'message' => 'CallSummarizer not initialized'
        ];
        $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
    }
    
    $data = [
        'status' => 'ready',
        'service' => 'AI Call Summarizer',
        'version' => '1.0.0',
        'features' => [
            'Hebrew audio transcription',
            'AI-powered call analysis',
            'Structured JSON output',
            'Batch processing support'
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
    return $response->withHeader('Content-Type', 'application/json');
});

/**
 * Process single call endpoint
 */
$app->post('/process-call', function (Request $request, Response $response) use ($summarizer) {
    if ($summarizer === null) {
        $data = [
            'error' => 'Service not available',
            'message' => 'CallSummarizer not initialized'
        ];
        $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
    }
    
    try {
        // Get JSON data
        $crmData = json_decode($request->getBody()->getContents(), true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $data = [
                'error' => 'Invalid JSON',
                'message' => 'Request body must be valid JSON'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        if (empty($crmData)) {
            $data = [
                'error' => 'No data provided',
                'message' => 'Request body cannot be empty'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        // Validate required fields
        if (!isset($crmData['recordingPath']) || empty($crmData['recordingPath'])) {
            $data = [
                'error' => 'Missing required field',
                'message' => 'recordingPath field is required and cannot be empty'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        // Process the call
        $callId = $crmData['callid'] ?? 'unknown';
        error_log("Processing call ID: {$callId}");
        
        $result = $summarizer->processCall($crmData);
        
        $response->getBody()->write(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        return $response->withHeader('Content-Type', 'application/json');
        
    } catch (Exception $e) {
        error_log("Error processing call: " . $e->getMessage());
        $data = [
            'error' => 'Internal server error',
            'message' => $e->getMessage()
        ];
        $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
    }
});

/**
 * Process multiple calls endpoint
 */
$app->post('/process-calls', function (Request $request, Response $response) use ($summarizer) {
    if ($summarizer === null) {
        $data = [
            'error' => 'Service not available',
            'message' => 'CallSummarizer not initialized'
        ];
        $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
    }
    
    try {
        // Get JSON data
        $requestData = json_decode($request->getBody()->getContents(), true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $data = [
                'error' => 'Invalid JSON',
                'message' => 'Request body must be valid JSON'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        if (empty($requestData) || !isset($requestData['calls'])) {
            $data = [
                'error' => 'Missing required field',
                'message' => 'calls array is required'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        $calls = $requestData['calls'];
        
        if (!is_array($calls)) {
            $data = [
                'error' => 'Invalid data type',
                'message' => 'calls must be an array'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        if (empty($calls)) {
            $data = [
                'error' => 'Empty array',
                'message' => 'calls array cannot be empty'
            ];
            $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
        
        // Process each call
        $results = [];
        $processedCount = 0;
        $failedCount = 0;
        
        foreach ($calls as $index => $callData) {
            try {
                // Validate individual call data
                if (!isset($callData['recordingPath']) || empty($callData['recordingPath'])) {
                    $results[] = [
                        'index' => $index,
                        'error' => 'recordingPath field is required and cannot be empty'
                    ];
                    $failedCount++;
                    continue;
                }
                
                // Process the call
                $callId = $callData['callid'] ?? 'unknown';
                error_log("Processing call " . ($index + 1) . "/" . count($calls) . ": {$callId}");
                
                $result = $summarizer->processCall($callData);
                $results[] = [
                    'index' => $index,
                    'result' => $result
                ];
                $processedCount++;
                
            } catch (Exception $e) {
                error_log("Error processing call {$index}: " . $e->getMessage());
                $results[] = [
                    'index' => $index,
                    'error' => $e->getMessage()
                ];
                $failedCount++;
            }
        }
        
        // Return batch results
        $data = [
            'total_calls' => count($calls),
            'processed_calls' => $processedCount,
            'failed_calls' => $failedCount,
            'results' => $results
        ];
        
        $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        return $response->withHeader('Content-Type', 'application/json');
        
    } catch (Exception $e) {
        error_log("Error processing batch calls: " . $e->getMessage());
        $data = [
            'error' => 'Internal server error',
            'message' => $e->getMessage()
        ];
        $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
    }
});

/**
 * Survey endpoints
 */
$app->get('/survey/{surveyId}', function (Request $request, Response $response, array $args) {
    $surveyId = $args['surveyId'];
    
    // Redirect to survey page
    $surveyUrl = $_ENV['SURVEY_BASE_URL'] . '/' . $surveyId;
    return $response->withRedirect($surveyUrl);
});

/**
 * Analytics dashboard
 */
$app->get('/analytics', function (Request $request, Response $response) {
    $analyticsUrl = $_ENV['SURVEY_BASE_URL'] . '/analytics.php';
    return $response->withRedirect($analyticsUrl);
});

/**
 * WhatsApp webhook for delivery status
 */
$app->post('/whatsapp-webhook', function (Request $request, Response $response) {
    $data = json_decode($request->getBody()->getContents(), true);
    
    if (empty($data)) {
        return $response->withJson(['error' => 'No data provided'], 400);
    }
    
    // Process WhatsApp delivery notifications
    $whatsappService = new \App\WhatsAppService();
    $surveyService = new \App\SurveyService();
    
    // Update survey status based on WhatsApp notifications
    // This would be implemented based on GreenAPI webhook format
    
    return $response->withJson(['status' => 'received']);
});

/**
 * 404 handler
 */
$app->map(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'], '/{routes:.+}', function (Request $request, Response $response) {
    $data = [
        'error' => 'Endpoint not found',
        'available_endpoints' => [
            'GET /health',
            'GET /status',
            'POST /process-call',
            'POST /process-calls',
            'GET /survey/{surveyId}',
            'GET /analytics',
            'POST /whatsapp-webhook'
        ]
    ];
    $response->getBody()->write(json_encode($data, JSON_PRETTY_PRINT));
    return $response->withHeader('Content-Type', 'application/json')->withStatus(404);
});

// Run app
$app->run(); 