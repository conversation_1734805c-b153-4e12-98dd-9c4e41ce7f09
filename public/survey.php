<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\SurveyService;
use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Get survey ID from URL
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));
$surveyId = end($pathParts);

if (empty($surveyId) || $surveyId === 'survey.php') {
    http_response_code(404);
    echo "Survey not found";
    exit;
}

$surveyService = new SurveyService();
$survey = $surveyService->getSurvey($surveyId);

if (!$survey) {
    http_response_code(404);
    echo "Survey not found or expired";
    exit;
}

if ($survey['survey_status'] !== 'pending') {
    $message = $survey['survey_status'] === 'completed' 
        ? "This survey has already been completed. Thank you!" 
        : "This survey has expired.";
    
    http_response_code(410);
    echo $message;
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $responses = [
        'satisfaction' => $_POST['satisfaction'] ?? null,
        'agent_helpfulness' => $_POST['agent_helpfulness'] ?? null,
        'call_quality' => $_POST['call_quality'] ?? null,
        'resolution' => $_POST['resolution'] ?? null,
        'comments' => $_POST['comments'] ?? null
    ];
    
    $result = $surveyService->submitSurvey($surveyId, $responses);
    
    if ($result['status'] === 'submitted') {
        $success = true;
    } else {
        $error = $result['error_message'];
    }
}
?>
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>סקר שביעות רצון - Call Survey</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 2em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 1.8em;
        }
        
        .call-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: right;
        }
        
        .call-info p {
            margin: 5px 0;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: right;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .rating-group {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .rating-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }
        
        .rating-option input[type="radio"] {
            display: none;
        }
        
        .rating-option label {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0;
        }
        
        .rating-option input[type="radio"]:checked + label {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .rating-option:hover label {
            border-color: #667eea;
        }
        
        textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .thank-you {
            text-align: center;
            padding: 40px 20px;
        }
        
        .thank-you h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .thank-you p {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (isset($success) && $success): ?>
            <div class="thank-you">
                <div class="logo">✅</div>
                <h2>תודה לך!</h2>
                <p>הסקר שלך נשלח בהצלחה. דעתך חשובה לנו מאוד!</p>
                <p>אנחנו נשתמש בתגובות שלך כדי לשפר את השירות שלנו.</p>
            </div>
        <?php else: ?>
            <div class="logo">📞</div>
            <h1>סקר שביעות רצון</h1>
            
            <div class="call-info">
                <p><strong>תאריך השיחה:</strong> <?= date('d/m/Y H:i', strtotime($survey['start_date'])) ?></p>
                <?php if ($survey['agent_name']): ?>
                    <p><strong>הנציג:</strong> <?= htmlspecialchars($survey['agent_name']) ?></p>
                <?php endif; ?>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="error-message">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label>באיזו מידה היית מרוצה מהשיחה?</label>
                    <div class="rating-group">
                        <?php for ($i = 1; $i <= 10; $i++): ?>
                            <div class="rating-option">
                                <input type="radio" name="satisfaction" value="<?= $i ?>" id="satisfaction_<?= $i ?>" required>
                                <label for="satisfaction_<?= $i ?>"><?= $i ?></label>
                            </div>
                        <?php endfor; ?>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 12px; color: #666;">
                        <span>לא מרוצה</span>
                        <span>מרוצה מאוד</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>האם הנציג היה מועיל?</label>
                    <div class="rating-group">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <div class="rating-option">
                                <input type="radio" name="agent_helpfulness" value="<?= $i ?>" id="helpful_<?= $i ?>" required>
                                <label for="helpful_<?= $i ?>"><?= $i ?></label>
                            </div>
                        <?php endfor; ?>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 12px; color: #666;">
                        <span>לא מועיל</span>
                        <span>מועיל מאוד</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>איכות השיחה</label>
                    <div class="rating-group">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <div class="rating-option">
                                <input type="radio" name="call_quality" value="<?= $i ?>" id="quality_<?= $i ?>" required>
                                <label for="quality_<?= $i ?>"><?= $i ?></label>
                            </div>
                        <?php endfor; ?>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 12px; color: #666;">
                        <span>גרועה</span>
                        <span>מצוינת</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>האם הבעיה שלך נפתרה?</label>
                    <div style="text-align: center;">
                        <label style="display: inline-block; margin: 0 10px;">
                            <input type="radio" name="resolution" value="yes" required> כן
                        </label>
                        <label style="display: inline-block; margin: 0 10px;">
                            <input type="radio" name="resolution" value="no" required> לא
                        </label>
                        <label style="display: inline-block; margin: 0 10px;">
                            <input type="radio" name="resolution" value="partially" required> חלקית
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>הערות נוספות (אופציונלי)</label>
                    <textarea name="comments" placeholder="שתף איתנו את המחשבות שלך..."></textarea>
                </div>
                
                <button type="submit" class="submit-btn">שלח סקר</button>
            </form>
        <?php endif; ?>
    </div>
    
    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const ratingOptions = document.querySelectorAll('.rating-option');
            
            ratingOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                });
            });
        });
    </script>
</body>
</html> 