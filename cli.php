<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\CallSummarizer;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

/**
 * CLI script for AI Call Summarizer
 * 
 * Usage:
 * php cli.php --help
 * php cli.php --test
 * php cli.php --process '{"callid": 123, "recordingPath": "https://..."}'
 */

// Parse command line arguments
$options = getopt('', ['help', 'test', 'process:', 'demo']);

if (isset($options['help'])) {
    showHelp();
    exit(0);
}

if (isset($options['demo'])) {
    showDemo();
    exit(0);
}

if (isset($options['test'])) {
    runTests();
    exit(0);
}

if (isset($options['process'])) {
    processCall($options['process']);
    exit(0);
}

// Default: show help
showHelp();
exit(1);

/**
 * Show help information
 */
function showHelp(): void
{
    echo "AI Call Summarizer CLI\n";
    echo "======================\n\n";
    echo "Usage:\n";
    echo "  php cli.php --help                    Show this help\n";
    echo "  php cli.php --test                    Run test scenarios\n";
    echo "  php cli.php --demo                    Show demo output format\n";
    echo "  php cli.php --process 'JSON_DATA'     Process a single call\n\n";
    echo "Examples:\n";
    echo "  php cli.php --process '{\"callid\": 123, \"recordingPath\": \"https://example.com/audio.mp3\"}'\n\n";
    echo "Environment Variables:\n";
    echo "  ASSEMBLYAI_API_KEY    AssemblyAI API key (required)\n";
    echo "  OPENAI_API_KEY        OpenAI API key (required)\n";
    echo "  OPENAI_MODEL          OpenAI model (default: gpt-4)\n\n";
}

/**
 * Show demo output format
 */
function showDemo(): void
{
    echo "Demo Output Format:\n";
    echo "==================\n\n";
    
    $exampleOutput = [
        'call_details' => [
            'callid' => 39128557,
            'recordingPath' => 'https://master.ippbx.co.il/example-audio-file.mp3',
            'start_date' => '2025-06-26 17:33:54',
            'call_direction' => 'OUTBOUND'
        ],
        'transcription_result' => [
            'status' => 'completed',
            'transcript_id' => 'abc123-def456',
            'text' => 'שלום, אני מתעניין במוצר שלכם...',
            'language_code' => 'he',
            'confidence' => 0.96,
            'error_message' => null
        ],
        'ai_analysis' => [
            'status' => 'completed',
            'summary' => 'Customer inquired about product features and pricing',
            'call_sentiment' => 'Positive',
            'main_topics' => ['Product inquiry', 'Pricing', 'Features'],
            'action_items' => ['Send product catalog', 'Follow up on pricing quote'],
            'customer_satisfaction_score' => 8,
            'error_message' => null
        ]
    ];
    
    echo json_encode($exampleOutput, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
}

/**
 * Run test scenarios
 */
function runTests(): void
{
    echo "Running Test Scenarios:\n";
    echo "======================\n\n";
    
    try {
        $summarizer = new CallSummarizer();
        echo "✅ CallSummarizer initialized successfully\n\n";
        
        // Test 1: Valid call data
        echo "Test 1: Valid call data\n";
        echo "------------------------\n";
        $testData = [
            'callid' => 39128557,
            'recordingPath' => 'https://master.ippbx.co.il/example-audio-file.mp3',
            'start_date' => '2025-06-26 17:33:54',
            'call_direction' => 'OUTBOUND'
        ];
        
        $result = $summarizer->processCall($testData);
        echo "Status: " . $result['transcription_result']['status'] . "\n";
        echo "AI Analysis Status: " . $result['ai_analysis']['status'] . "\n";
        
        if ($result['transcription_result']['status'] === 'completed') {
            echo "Transcript Length: " . strlen($result['transcription_result']['text']) . " characters\n";
        }
        
        if ($result['ai_analysis']['status'] === 'completed') {
            echo "Summary: " . substr($result['ai_analysis']['summary'], 0, 100) . "...\n";
            echo "Sentiment: " . $result['ai_analysis']['call_sentiment'] . "\n";
            echo "Satisfaction Score: " . $result['ai_analysis']['customer_satisfaction_score'] . "\n";
        }
        
        echo "\n";
        
        // Test 2: Missing recording path
        echo "Test 2: Missing recording path\n";
        echo "-------------------------------\n";
        $testData2 = [
            'callid' => 39128558,
            'start_date' => '2025-06-26 17:33:54',
            'call_direction' => 'OUTBOUND'
        ];
        
        $result2 = $summarizer->processCall($testData2);
        echo "Status: " . $result2['transcription_result']['status'] . "\n";
        echo "Error: " . $result2['transcription_result']['error_message'] . "\n\n";
        
        // Test 3: Empty recording path
        echo "Test 3: Empty recording path\n";
        echo "----------------------------\n";
        $testData3 = [
            'callid' => 39128559,
            'recordingPath' => '',
            'start_date' => '2025-06-26 17:33:54',
            'call_direction' => 'OUTBOUND'
        ];
        
        $result3 = $summarizer->processCall($testData3);
        echo "Status: " . $result3['transcription_result']['status'] . "\n";
        echo "Error: " . $result3['transcription_result']['error_message'] . "\n\n";
        
        echo "✅ All tests completed\n";
        
    } catch (Exception $e) {
        echo "❌ Test failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}

/**
 * Process a single call
 */
function processCall(string $jsonData): void
{
    try {
        $crmData = json_decode($jsonData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
            exit(1);
        }
        
        if (empty($crmData)) {
            echo "❌ Empty JSON data\n";
            exit(1);
        }
        
        $summarizer = new CallSummarizer();
        $result = $summarizer->processCall($crmData);
        
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
} 