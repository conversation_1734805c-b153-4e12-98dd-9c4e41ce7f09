{"name": "ben-levy/ai-call-summarizer", "description": "AI-powered call transcription and analysis system for Hebrew audio with WhatsApp survey integration", "type": "project", "require": {"php": ">=8.0", "guzzlehttp/guzzle": "^7.0", "vlucas/phpdotenv": "^5.0", "monolog/monolog": "^3.0", "slim/slim": "^4.0", "slim/psr7": "^1.0", "php-di/php-di": "^7.0", "ramsey/uuid": "^4.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "start": "php -S localhost:8000 -t public", "setup-db": "php scripts/setup_database.php"}, "config": {"optimize-autoloader": true, "sort-packages": true}}